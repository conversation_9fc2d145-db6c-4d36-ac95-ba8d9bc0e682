{"$schema": "https://schema.tauri.app/config/2", "productName": "Colony", "version": "1.1.1", "identifier": "com.colony.gui", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../build"}, "app": {"windows": [{"title": "Colony", "width": 1024, "height": 700, "minWidth": 1024, "dragDropEnabled": false, "maximized": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "createUpdaterArtifacts": false, "publisher": "Colony", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon-1024x1024.png", "icons/icon.icns", "icons/icon.ico"], "resources": [], "externalBin": ["binaries/dweb"], "copyright": "Copyright © 2025 <PERSON>. All rights reserved.", "category": "Utility", "shortDescription": "Colony - Autonomi Semantic Search Engine and File Manager", "longDescription": "A desktop application for interacting with the Autonomi network", "linux": {"appimage": {"bundleMediaFramework": true}, "deb": {"depends": []}}, "macOS": {"signingIdentity": "Developer ID Application: <PERSON> (3364NM68HH)", "hardenedRuntime": true, "entitlements": "entitlements.plist", "exceptionDomain": null, "frameworks": [], "minimumSystemVersion": "10.13", "dmg": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"width": 660, "height": 400}}}, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "tsp": false, "webviewInstallMode": {"type": "downloadBootstrapper"}, "nsis": {"installMode": "perMachine", "displayLanguageSelector": true}, "wix": {"language": "en-US"}}}}