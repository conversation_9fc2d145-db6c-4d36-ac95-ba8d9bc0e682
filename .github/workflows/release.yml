name: Release

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      tag_name:
        description: 'Tag name for the release (e.g., v1.0.0-test)'
        required: true
        default: ''
        type: string

permissions:
  contents: write

env:
  CARGO_TERM_COLOR: always

jobs:
  checks:
    name: Run checks
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri

      - name: Install dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libappindicator3-dev librsvg2-dev patchelf libglib2.0-dev libgtk-3-dev

      - name: Install frontend dependencies
        run: npm ci

      #FIXME: reenable this check when all errors/warnings are resolved
      #- name: Check frontend
      #  run: npm run check

      - name: Build frontend
        run: npm run build

      - name: Check Rust formatting
        run: cargo fmt --all --check
        working-directory: src-tauri

      - name: Run Clippy
        run: cargo clippy --all-targets --all-features -- -D warnings
        working-directory: src-tauri

      - name: Run Rust tests
        run: cargo test
        working-directory: src-tauri

  build:
    name: Build and Release
    needs: checks
    strategy:
      fail-fast: false
      matrix:
        include:
          - platform: 'macos-latest'
            args: '--target aarch64-apple-darwin'
            target: 'aarch64-apple-darwin'
            arch: 'aarch64'
          - platform: 'macos-latest'
            args: '--target x86_64-apple-darwin'
            target: 'x86_64-apple-darwin'
            arch: 'x86_64'
          - platform: 'ubuntu-22.04'
            args: '--target x86_64-unknown-linux-gnu'
            target: 'x86_64-unknown-linux-gnu'
          - platform: 'windows-latest'
            args: '--target x86_64-pc-windows-msvc'
            target: 'x86_64-pc-windows-msvc'

    runs-on: ${{ matrix.platform }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri

      - name: Install dependencies (Ubuntu only)
        if: matrix.platform == 'ubuntu-22.04'
        run: |
          sudo apt-get update
          sudo apt-get install -y libwebkit2gtk-4.1-dev libayatana-appindicator3-dev librsvg2-dev patchelf libglib2.0-dev libgtk-3-dev

      - name: Install frontend dependencies
        run: npm ci

      - name: Build frontend
        run: npm run build

      - name: Configure Windows static linking
        if: matrix.platform == 'windows-latest'
        run: |
          echo "RUSTFLAGS=-C target-feature=+crt-static -C opt-level=3" >> $GITHUB_ENV
          echo "CARGO_BUILD_TARGET=x86_64-pc-windows-msvc" >> $GITHUB_ENV
          echo "CARGO_PROFILE_RELEASE_LTO=true" >> $GITHUB_ENV
        shell: bash

      - name: Set SDKROOT and CPLUS_INCLUDE_PATH for macOS
        if: matrix.platform == 'macos-latest'
        run: |
          export SDKROOT=$(xcrun --sdk macosx --show-sdk-path)
          echo "SDKROOT=$SDKROOT" >> $GITHUB_ENV
          echo "CPLUS_INCLUDE_PATH=$SDKROOT/usr/include/c++/v1" >> $GITHUB_ENV

      - name: Import Apple Certificates
        if: matrix.platform == 'macos-latest'
        run: |
          # Create a temporary keychain
          KEYCHAIN_PATH="$HOME/Library/Keychains/signing_temp.keychain"
          KEYCHAIN_PASSWORD=$(openssl rand -base64 32)

          # Create keychain
          security create-keychain -p "$KEYCHAIN_PASSWORD" signing_temp.keychain
          security set-keychain-settings -lut 21600 signing_temp.keychain
          security unlock-keychain -p "$KEYCHAIN_PASSWORD" signing_temp.keychain

          # Add to keychain search list
          security list-keychains -d user -s signing_temp.keychain $(security list-keychains -d user | sed s/\"//g)

          # Import Developer ID Application certificate (for DMG distribution)
          echo "${{ secrets.APPLE_CERTIFICATE }}" | base64 --decode > /tmp/cert1.p12
          security import /tmp/cert1.p12 -k signing_temp.keychain -P "${{ secrets.APPLE_CERTIFICATE_PASSWORD }}" -A

          # Import 3rd Party Mac Developer Application certificate (for App Store)
          if [ -n "${{ secrets.APPLE_APP_STORE_CERTIFICATE }}" ]; then
            echo "${{ secrets.APPLE_APP_STORE_CERTIFICATE }}" | base64 --decode > /tmp/cert_appstore.p12
            security import /tmp/cert_appstore.p12 -k signing_temp.keychain -P "${{ secrets.APPLE_APP_STORE_CERTIFICATE_PASSWORD }}" -A
          fi

          # Import Installer certificate
          echo "${{ secrets.APPLE_INSTALLER_CERTIFICATE }}" | base64 --decode > /tmp/cert2.p12
          security import /tmp/cert2.p12 -k signing_temp.keychain -P "${{ secrets.APPLE_INSTALLER_CERTIFICATE_PASSWORD }}" -A

          # Set partition list for both certificates
          security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k "$KEYCHAIN_PASSWORD" signing_temp.keychain

          # Clean up temporary files
          rm -f /tmp/cert1.p12 /tmp/cert2.p12 /tmp/cert_appstore.p12

          # Store keychain password for later use
          echo "KEYCHAIN_PASSWORD=$KEYCHAIN_PASSWORD" >> $GITHUB_ENV

      - name: Download Apple Provisioning Profile
        if: matrix.platform == 'macos-latest'
        run: |
          echo "${{ secrets.APPLE_PROVISIONING_PROFILE }}" | base64 --decode > embedded.provisionprofile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp embedded.provisionprofile ~/Library/MobileDevice/Provisioning\ Profiles/

      - name: Prepare macOS Icons
        if: matrix.platform == 'macos-latest'
        run: |
          # Create iconset directory
          mkdir -p Colony.iconset

          # Copy existing icons
          cp src-tauri/icons/32x32.png Colony.iconset/icon_32x32.png
          cp src-tauri/icons/128x128.png Colony.iconset/icon_128x128.png
          cp src-tauri/icons/<EMAIL> Colony.iconset/<EMAIL>

          # Add the 1024x1024 icon for App Store
          cp src-tauri/icons/icon-1024x1024.png Colony.iconset/icon_1024x1024.png

          # Generate additional required sizes from the 1024x1024 icon
          sips -z 16 16 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/icon_16x16.png
          sips -z 32 32 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>
          sips -z 64 64 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>
          sips -z 256 256 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>
          sips -z 256 256 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/icon_256x256.png
          sips -z 512 512 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>
          sips -z 512 512 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/icon_512x512.png
          sips -z 1024 1024 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>

          # Create new .icns file with all sizes including 1024x1024
          iconutil -c icns Colony.iconset -o src-tauri/icons/icon.icns

          # Clean up
          rm -rf Colony.iconset

          echo "Updated icon.icns with 1024x1024 icon for App Store compatibility"

      - name: Build Tauri app
        uses: tauri-apps/tauri-action@v0
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          APPLE_CERTIFICATE: ${{ secrets.APPLE_CERTIFICATE }}
          APPLE_CERTIFICATE_PASSWORD: ${{ secrets.APPLE_CERTIFICATE_PASSWORD }}
          APPLE_SIGNING_IDENTITY: ${{ secrets.APPLE_SIGNING_IDENTITY }}
          APPLE_ID: ${{ secrets.APPLE_ID }}
          APPLE_PASSWORD: ${{ secrets.APPLE_PASSWORD }}
          APPLE_TEAM_ID: ${{ secrets.APPLE_TEAM_ID }}
        with:
          tagName: ${{ github.event.inputs.tag_name || github.ref_name }}
          releaseName: 'Colony ${{ github.event.inputs.tag_name || github.ref_name }}'
          releaseBody: |
            ## Colony v__VERSION__

            ### Installation

            **Linux:**
            - Download the `.AppImage` file for a portable application
            - Download the `.deb` file for Debian/Ubuntu systems
            - Download the `.rpm` file for Red Hat/Fedora systems

            **macOS:**
            - Download the `.dmg` file and drag Colony to your Applications folder

            **Windows:**
            - Download the `.msi` file and run the installer

            ### Changes
            See the commit history for detailed changes in this release.
          releaseDraft: false
          prerelease: false
          includeUpdaterJson: false
          args: ${{ matrix.args }}
          bundleIdentifier: 'com.colony.gui'

      - name: Debug App Bundle Structure
        if: matrix.platform == 'macos-latest'
        run: |
          # Find the built .app bundle
          APP_PATH=$(find src-tauri/target/${{ matrix.target }}/release/bundle/macos -name "*.app" | head -1)
          if [ -z "$APP_PATH" ]; then
            echo "No .app bundle found"
            exit 1
          fi

          echo "Found app bundle: $APP_PATH"
          echo "Bundle structure:"
          ls -la "$APP_PATH/Contents/"

          INFO_PLIST="$APP_PATH/Contents/Info.plist"
          if [ -f "$INFO_PLIST" ]; then
            echo "Info.plist exists. Contents:"
            cat "$INFO_PLIST"
            echo ""
            echo "Checking specific keys:"
            /usr/libexec/PlistBuddy -c "Print :CFBundlePackageType" "$INFO_PLIST" || echo "CFBundlePackageType not found"
            /usr/libexec/PlistBuddy -c "Print :CFBundleIdentifier" "$INFO_PLIST" || echo "CFBundleIdentifier not found"
            /usr/libexec/PlistBuddy -c "Print :CFBundleExecutable" "$INFO_PLIST" || echo "CFBundleExecutable not found"
          else
            echo "Info.plist NOT FOUND at $INFO_PLIST"
            echo "Contents of Contents directory:"
            ls -la "$APP_PATH/Contents/"
          fi

      - name: Notarize DMG for Distribution
        if: matrix.platform == 'macos-latest'
        run: |
          # Find the built .app bundle
          APP_PATH=$(find src-tauri/target/${{ matrix.target }}/release/bundle/macos -name "*.app" | head -1)
          if [ -z "$APP_PATH" ]; then
            echo "No .app bundle found"
            exit 1
          fi

          echo "Found app bundle: $APP_PATH"

          # Create API key file for App Store Connect
          mkdir -p ~/.appstoreconnect/private_keys
          echo "${{ secrets.APP_STORE_CONNECT_API_KEY }}" | base64 --decode > ~/.appstoreconnect/private_keys/AuthKey_${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}.p8

          # Create a zip archive for notarization
          APP_NAME=$(basename "$APP_PATH")
          ZIP_PATH="${APP_PATH%/*}/${APP_NAME%.app}.zip"
          ditto -c -k --keepParent "$APP_PATH" "$ZIP_PATH"

          echo "Created zip archive: $ZIP_PATH"

          # Verify code signing before notarization
          echo "Verifying code signature..."
          codesign -v -v --deep --strict "$APP_PATH"
          if [ $? -ne 0 ]; then
            echo "Code signature verification failed!"
            exit 1
          fi

          # Check if hardened runtime is enabled
          echo "Checking hardened runtime..."
          codesign -d --entitlements - "$APP_PATH"

          # Submit for notarization
          echo "Submitting for notarization..."
          SUBMISSION_ID=$(xcrun notarytool submit "$ZIP_PATH" \
            --key ~/.appstoreconnect/private_keys/AuthKey_${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}.p8 \
            --key-id ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }} \
            --issuer ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }} \
            --wait \
            --timeout 30m \
            --output-format json | jq -r '.id')

          # Check notarization status and get detailed logs if it failed
          STATUS=$(xcrun notarytool info "$SUBMISSION_ID" \
            --key ~/.appstoreconnect/private_keys/AuthKey_${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}.p8 \
            --key-id ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }} \
            --issuer ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }} \
            --output-format json | jq -r '.status')

          echo "Notarization status: $STATUS"

          if [ "$STATUS" != "Accepted" ]; then
            echo "Notarization failed. Getting detailed logs..."
            xcrun notarytool log "$SUBMISSION_ID" \
              --key ~/.appstoreconnect/private_keys/AuthKey_${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}.p8 \
              --key-id ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }} \
              --issuer ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }}
            exit 1
          fi

          # Staple the notarization ticket
          echo "Stapling notarization ticket..."
          xcrun stapler staple "$APP_PATH"

          # Verify notarization
          echo "Verifying notarization..."
          xcrun stapler validate "$APP_PATH"
          spctl -a -v "$APP_PATH"

          # Create final DMG with notarized app (this is already done by Tauri, but we verify it)
          DMG_PATH=$(find src-tauri/target/${{ matrix.target }}/release/bundle/dmg -name "*.dmg" | head -1)
          if [ -n "$DMG_PATH" ]; then
            echo "Found DMG: $DMG_PATH"
            # Notarize the DMG as well
            xcrun notarytool submit "$DMG_PATH" \
              --key ~/.appstoreconnect/private_keys/AuthKey_${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}.p8 \
              --key-id ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }} \
              --issuer ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }} \
              --wait \
              --timeout 30m \
              --verbose

            # Staple the DMG
            xcrun stapler staple "$DMG_PATH"
          fi

      - name: Upload macOS artifacts for App Store build
        if: matrix.platform == 'macos-latest'
        uses: actions/upload-artifact@v4
        with:
          name: macos-${{ matrix.arch }}-artifacts
          path: |
            src-tauri/target/${{ matrix.target }}/release/bundle/macos/*.app
            src-tauri/target/${{ matrix.target }}/release/colony-app
            src-tauri/binaries/dweb-${{ matrix.target }}
          retention-days: 1

      - name: Verify build artifacts
        run: |
          echo "Checking build artifacts..."
          if [ "${{ matrix.platform }}" = "windows-latest" ]; then
            echo "Windows executable files:"
            find src-tauri/target -name "*.exe" -exec ls -lh {} \;
            echo "Windows installer files:"
            find . -name "*.msi" -exec ls -lh {} \;
            echo "Checking dependencies of main executable:"
            find src-tauri/target -name "colony.exe" -exec file {} \;
          elif [ "${{ matrix.platform }}" = "ubuntu-22.04" ]; then
            echo "Linux AppImage files:"
            find . -name "*.AppImage" -exec ls -lh {} \;
            echo "Linux DEB files:"
            find . -name "*.deb" -exec ls -lh {} \;
          elif [ "${{ matrix.platform }}" = "macos-latest" ]; then
            echo "macOS DMG files:"
            find . -name "*.dmg" -exec ls -lh {} \;
          fi
        shell: bash

      - name: Cleanup Keychain
        if: matrix.platform == 'macos-latest' && always()
        run: |
          # Remove the temporary keychain
          security delete-keychain signing_temp.keychain || true
        shell: bash

  app-store-build:
    name: Build Universal App for App Store
    needs: build
    runs-on: macos-latest
    if: contains(github.ref, 'refs/tags/') || github.event_name == 'workflow_dispatch'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Setup Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: aarch64-apple-darwin,x86_64-apple-darwin

      - name: Cache Rust dependencies
        uses: Swatinem/rust-cache@v2
        with:
          workspaces: src-tauri

      - name: Install frontend dependencies
        run: npm ci

      - name: Build frontend
        run: npm run build

      - name: Fetch binaries for App Store build
        run: |
          cd src-tauri
          # Run the fetch script to ensure we have the dweb binaries
          bash binaries/fetch_binaries.sh

      - name: Download macOS artifacts
        uses: actions/download-artifact@v4
        with:
          pattern: macos-*-artifacts
          merge-multiple: true
          path: artifacts/

      - name: Set SDKROOT and CPLUS_INCLUDE_PATH for macOS
        run: |
          export SDKROOT=$(xcrun --sdk macosx --show-sdk-path)
          echo "SDKROOT=$SDKROOT" >> $GITHUB_ENV
          echo "CPLUS_INCLUDE_PATH=$SDKROOT/usr/include/c++/v1" >> $GITHUB_ENV

      - name: Import Apple Certificates for App Store
        run: |
          # Create a temporary keychain
          KEYCHAIN_PATH="$HOME/Library/Keychains/appstore_temp.keychain"
          KEYCHAIN_PASSWORD=$(openssl rand -base64 32)

          # Create keychain
          security create-keychain -p "$KEYCHAIN_PASSWORD" appstore_temp.keychain
          security set-keychain-settings -lut 21600 appstore_temp.keychain
          security unlock-keychain -p "$KEYCHAIN_PASSWORD" appstore_temp.keychain

          # Add to keychain search list
          security list-keychains -d user -s appstore_temp.keychain $(security list-keychains -d user | sed s/\"//g)

          # Import 3rd Party Mac Developer Application certificate (for App Store)
          echo "${{ secrets.APPLE_APP_STORE_CERTIFICATE }}" | base64 --decode > /tmp/cert_appstore.p12
          security import /tmp/cert_appstore.p12 -k appstore_temp.keychain -P "${{ secrets.APPLE_APP_STORE_CERTIFICATE_PASSWORD }}" -A

          # Import 3rd Party Mac Developer Installer certificate
          echo "${{ secrets.APPLE_INSTALLER_CERTIFICATE }}" | base64 --decode > /tmp/cert_installer.p12
          security import /tmp/cert_installer.p12 -k appstore_temp.keychain -P "${{ secrets.APPLE_INSTALLER_CERTIFICATE_PASSWORD }}" -A

          # Set partition list for certificates
          security set-key-partition-list -S apple-tool:,apple:,codesign: -s -k "$KEYCHAIN_PASSWORD" appstore_temp.keychain

          # Clean up temporary files
          rm -f /tmp/cert_appstore.p12 /tmp/cert_installer.p12

          # Store keychain password for later use
          echo "KEYCHAIN_PASSWORD=$KEYCHAIN_PASSWORD" >> $GITHUB_ENV

      - name: Download Apple Provisioning Profile
        run: |
          echo "${{ secrets.APPLE_PROVISIONING_PROFILE }}" | base64 --decode > embedded.provisionprofile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp embedded.provisionprofile ~/Library/MobileDevice/Provisioning\ Profiles/

      - name: Prepare macOS Icons
        run: |
          # Create iconset directory
          mkdir -p Colony.iconset

          # Copy existing icons
          cp src-tauri/icons/32x32.png Colony.iconset/icon_32x32.png
          cp src-tauri/icons/128x128.png Colony.iconset/icon_128x128.png
          cp src-tauri/icons/<EMAIL> Colony.iconset/<EMAIL>

          # Add the 1024x1024 icon for App Store
          cp src-tauri/icons/icon-1024x1024.png Colony.iconset/icon_1024x1024.png

          # Generate additional required sizes from the 1024x1024 icon
          sips -z 16 16 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/icon_16x16.png
          sips -z 32 32 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>
          sips -z 64 64 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>
          sips -z 256 256 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>
          sips -z 256 256 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/icon_256x256.png
          sips -z 512 512 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>
          sips -z 512 512 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/icon_512x512.png
          sips -z 1024 1024 src-tauri/icons/icon-1024x1024.png --out Colony.iconset/<EMAIL>

          # Create new .icns file with all sizes including 1024x1024
          iconutil -c icns Colony.iconset -o src-tauri/icons/icon.icns

          # Clean up
          rm -rf Colony.iconset

          echo "Updated icon.icns with 1024x1024 icon for App Store compatibility"

      - name: Create Universal Binary and App Store Build
        run: |
          # Debug: Show what artifacts we have
          echo "=== Artifact contents ==="
          find artifacts -type f -name "*" | head -20
          echo "========================="

          # Create universal dweb binary from downloaded artifacts
          echo "Creating universal dweb binary..."
          mkdir -p src-tauri/binaries

          # Find the architecture-specific dweb binaries from artifacts
          DWEB_ARM64=$(find artifacts -name "dweb-aarch64-apple-darwin" | head -1)
          DWEB_X86_64=$(find artifacts -name "dweb-x86_64-apple-darwin" | head -1)

          if [ -z "$DWEB_ARM64" ] || [ -z "$DWEB_X86_64" ]; then
            echo "Could not find architecture-specific dweb binaries in artifacts"
            echo "All dweb files found:"
            find artifacts -name "*dweb*" -type f
            echo "All files in artifacts:"
            find artifacts -type f | grep -E "(dweb|colony-app)"
            exit 1
          fi

          echo "Found ARM64 dweb: $DWEB_ARM64"
          echo "Found x86_64 dweb: $DWEB_X86_64"

          # Create universal dweb binary
          lipo -create "$DWEB_ARM64" "$DWEB_X86_64" -output src-tauri/binaries/dweb
          chmod +x src-tauri/binaries/dweb

          echo "Created universal dweb binary"
          file src-tauri/binaries/dweb

          # Create universal main binary
          echo "Creating universal main binary..."
          MAIN_ARM64=$(find artifacts -name "colony-app" | head -1)
          MAIN_X86_64=$(find artifacts -name "colony-app" | tail -1)

          # Verify we have two different binaries
          if [ -z "$MAIN_ARM64" ] || [ -z "$MAIN_X86_64" ] || [ "$MAIN_ARM64" = "$MAIN_X86_64" ]; then
            echo "Could not find both main binaries in artifacts"
            echo "All colony-app files found:"
            find artifacts -name "colony-app" -type f
            exit 1
          fi

          echo "Found ARM64 main: $MAIN_ARM64"
          echo "Found x86_64 main: $MAIN_X86_64"

          # Create directory for universal binary
          mkdir -p src-tauri/target/universal-apple-darwin/release
          lipo -create "$MAIN_ARM64" "$MAIN_X86_64" -output src-tauri/target/universal-apple-darwin/release/colony-app
          chmod +x src-tauri/target/universal-apple-darwin/release/colony-app

          echo "Created universal main binary"
          file src-tauri/target/universal-apple-darwin/release/colony-app

      - name: Build App Store Bundle
        run: |
          # Create a temporary tauri config for App Store
          cp src-tauri/tauri.conf.json src-tauri/tauri.conf.appstore.json

          # Update the config for App Store signing
          cat src-tauri/tauri.conf.appstore.json | \
            jq '.bundle.macOS.signingIdentity = "3rd Party Mac Developer Application: Charles McClish (3364NM68HH)" | .bundle.macOS.entitlements = "entitlements.appstore.plist"' > \
            src-tauri/tauri.conf.appstore.tmp.json && \
            mv src-tauri/tauri.conf.appstore.tmp.json src-tauri/tauri.conf.appstore.json

          cd src-tauri

          # Build for App Store with custom config
          cargo tauri build --config tauri.conf.appstore.json --target universal-apple-darwin

      - name: Sign App Store Bundle and Sidecar
        run: |
          # Find the built .app bundle
          APP_PATH=$(find src-tauri/target/universal-apple-darwin/release/bundle/macos -name "*.app" | head -1)
          if [ -z "$APP_PATH" ]; then
            echo "No .app bundle found"
            exit 1
          fi

          echo "Found app bundle: $APP_PATH"

          # Sign the dweb sidecar with App Store certificate
          DWEB_PATH="$APP_PATH/Contents/MacOS/dweb"
          if [ -f "$DWEB_PATH" ]; then
            echo "Signing dweb sidecar..."
            codesign --force --sign "3rd Party Mac Developer Application: Charles McClish (3364NM68HH)" \
              --entitlements src-tauri/entitlements.appstore.plist \
              "$DWEB_PATH"

            # Verify dweb signature
            codesign -v -v --deep --strict "$DWEB_PATH"
            echo "dweb sidecar signed successfully"
          else
            echo "Warning: dweb sidecar not found at $DWEB_PATH"
          fi

          # Re-sign the main app bundle with App Store certificate
          echo "Signing main app bundle..."
          codesign --force --sign "3rd Party Mac Developer Application: Charles McClish (3364NM68HH)" \
            --entitlements src-tauri/entitlements.appstore.plist \
            --deep \
            "$APP_PATH"

          # Verify app signature
          echo "Verifying app signature..."
          codesign -v -v --deep --strict "$APP_PATH"

          # Check entitlements
          echo "Checking entitlements..."
          codesign -d --entitlements - "$APP_PATH"

      - name: Create PKG for App Store Connect
        run: |
          # Find the signed .app bundle
          APP_PATH=$(find src-tauri/target/universal-apple-darwin/release/bundle/macos -name "*.app" | head -1)

          # Create API key file for App Store Connect
          mkdir -p ~/.appstoreconnect/private_keys
          echo "${{ secrets.APP_STORE_CONNECT_API_KEY }}" | base64 --decode > ~/.appstoreconnect/private_keys/AuthKey_${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}.p8

          # Create PKG installer for App Store Connect upload
          echo "Creating PKG installer for App Store Connect..."
          PKG_PATH="${APP_PATH%/*}/Colony_${{ github.event.inputs.tag_name || github.ref_name }}_Universal.pkg"

          # Use productbuild to create a proper installer package
          productbuild --component "$APP_PATH" /Applications \
            --sign "3rd Party Mac Developer Installer: Charles McClish (3364NM68HH)" \
            "$PKG_PATH"

          if [ ! -f "$PKG_PATH" ]; then
            echo "Failed to create PKG installer"
            exit 1
          fi

          echo "Created PKG installer: $PKG_PATH"

          # Verify PKG signature
          echo "Verifying PKG signature..."
          pkgutil --check-signature "$PKG_PATH"

          # Upload PKG to App Store Connect using xcrun altool
          echo "Uploading PKG to App Store Connect..."
          xcrun altool --upload-app \
            --type osx \
            --file "$PKG_PATH" \
            --apiKey ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }} \
            --apiIssuer ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }} \
            --verbose

      - name: Cleanup App Store Keychain
        if: always()
        run: |
          # Remove the temporary keychain
          security delete-keychain appstore_temp.keychain || true
        shell: bash
